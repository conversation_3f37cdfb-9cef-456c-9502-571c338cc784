const Joi = require('joi');
const logger = require('../utils/logger');

/**
 * Generic validation middleware
 * @param {Object} schema - Joi validation schema
 * @param {string} property - Request property to validate ('body', 'params', 'query')
 * @returns {Function} - Middleware function
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      logger.warn('Validation error:', { 
        property, 
        errors: errorDetails,
        originalData: req[property] 
      });

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: errorDetails
        },
        timestamp: new Date().toISOString()
      });
    }

    // Replace the original data with the validated and sanitized data
    req[property] = value;
    next();
  };
};

// Authentication validation schemas
const loginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  password: Joi.string()
    .min(6)
    .required()
    .messages({
      'string.min': 'Password must be at least 6 characters long',
      'any.required': 'Password is required'
    }),
  userType: Joi.string()
    .valid('admin', 'member')
    .default('member')
    .messages({
      'any.only': 'User type must be either admin or member'
    })
});

const registerAdminSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(255)
    .required()
    .messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 255 characters',
      'any.required': 'Name is required'
    }),
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  password: Joi.string()
    .min(6)
    .max(255)
    .required()
    .messages({
      'string.min': 'Password must be at least 6 characters long',
      'string.max': 'Password cannot exceed 255 characters',
      'any.required': 'Password is required'
    }),
  role: Joi.string()
    .valid('admin', 'super_admin')
    .default('admin')
    .messages({
      'any.only': 'Role must be either admin or super_admin'
    })
});

const registerMemberSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(255)
    .required()
    .messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 255 characters',
      'any.required': 'Name is required'
    }),
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  password: Joi.string()
    .min(6)
    .max(255)
    .required()
    .messages({
      'string.min': 'Password must be at least 6 characters long',
      'string.max': 'Password cannot exceed 255 characters',
      'any.required': 'Password is required'
    })
});

const changePasswordSchema = Joi.object({
  oldPassword: Joi.string()
    .required()
    .messages({
      'any.required': 'Current password is required'
    }),
  newPassword: Joi.string()
    .min(6)
    .max(255)
    .required()
    .messages({
      'string.min': 'New password must be at least 6 characters long',
      'string.max': 'New password cannot exceed 255 characters',
      'any.required': 'New password is required'
    })
});

// Trainer validation schemas
const createTrainerSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(255)
    .required()
    .messages({
      'string.min': 'Trainer name must be at least 2 characters long',
      'string.max': 'Trainer name cannot exceed 255 characters',
      'any.required': 'Trainer name is required'
    }),
  systemPrompt: Joi.string()
    .min(10)
    .required()
    .messages({
      'string.min': 'System prompt must be at least 10 characters long',
      'any.required': 'System prompt is required'
    }),
  description: Joi.string()
    .max(1000)
    .optional()
    .messages({
      'string.max': 'Description cannot exceed 1000 characters'
    }),
  status: Joi.string()
    .valid('active', 'inactive')
    .default('active')
    .messages({
      'any.only': 'Status must be either active or inactive'
    })
});

const updateTrainerSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(255)
    .optional()
    .messages({
      'string.min': 'Trainer name must be at least 2 characters long',
      'string.max': 'Trainer name cannot exceed 255 characters'
    }),
  systemPrompt: Joi.string()
    .min(10)
    .optional()
    .messages({
      'string.min': 'System prompt must be at least 10 characters long',
    }),
  description: Joi.string()
    .max(1000)
    .optional()
    .messages({
      'string.max': 'Description cannot exceed 1000 characters'
    }),
  status: Joi.string()
    .valid('active', 'inactive')
    .optional()
    .messages({
      'any.only': 'Status must be either active or inactive'
    })
});

const assignTrainerSchema = Joi.object({
  memberIds: Joi.array()
    .items(Joi.number().integer().positive())
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one member ID is required',
      'any.required': 'Member IDs are required'
    })
});

// Chat validation schemas
const sendMessageSchema = Joi.object({
  message: Joi.string()
    .min(1)
    .max(5000)
    .required()
    .messages({
      'string.min': 'Message cannot be empty',
      'string.max': 'Message cannot exceed 5000 characters',
      'any.required': 'Message is required'
    }),
  sessionId: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': 'Session ID must be a valid UUID',
      'any.required': 'Session ID is required'
    })
});

// Parameter validation schemas
const idParamSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID must be a number',
      'number.integer': 'ID must be an integer',
      'number.positive': 'ID must be positive',
      'any.required': 'ID is required'
    })
});

const trainerIdParamSchema = Joi.object({
  trainerId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'Trainer ID must be a number',
      'number.integer': 'Trainer ID must be an integer',
      'number.positive': 'Trainer ID must be positive',
      'any.required': 'Trainer ID is required'
    })
});

// Query validation schemas
const paginationSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  search: Joi.string()
    .max(255)
    .optional()
    .messages({
      'string.max': 'Search query cannot exceed 255 characters'
    })
});

// Export validation middleware functions
module.exports = {
  // Generic validation
  validate,
  
  // Authentication validations
  validateLogin: validate(loginSchema),
  validateRegisterAdmin: validate(registerAdminSchema),
  validateRegisterMember: validate(registerMemberSchema),
  validateChangePassword: validate(changePasswordSchema),
  
  // Trainer validations
  validateCreateTrainer: validate(createTrainerSchema),
  validateUpdateTrainer: validate(updateTrainerSchema),
  validateAssignTrainer: validate(assignTrainerSchema),
  
  // Chat validations
  validateSendMessage: validate(sendMessageSchema),
  
  // Parameter validations
  validateIdParam: validate(idParamSchema, 'params'),
  validateTrainerIdParam: validate(trainerIdParamSchema, 'params'),
  
  // Query validations
  validatePagination: validate(paginationSchema, 'query'),
  
  // Schemas (for reuse)
  schemas: {
    loginSchema,
    registerAdminSchema,
    registerMemberSchema,
    changePasswordSchema,
    createTrainerSchema,
    updateTrainerSchema,
    assignTrainerSchema,
    sendMessageSchema,
    idParamSchema,
    trainerIdParamSchema,
    paginationSchema
  }
}; 