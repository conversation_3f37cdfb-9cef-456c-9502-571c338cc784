module.exports = (sequelize, DataTypes) => {
  const Trainer = sequelize.define('Trainer', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    adminId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'admins',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 255]
      }
    },
    systemPrompt: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: { len: [10] },
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true
    }
  }, {
    tableName: 'trainers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['adminId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  Trainer.associate = (models) => {
    // Trainer belongs to an admin
    Trainer.belongsTo(models.Admin, {
      foreignKey: 'adminId',
      as: 'admin'
    });

    // Trainer has many submodules
    Trainer.hasMany(models.TrainerSubmodule, {
      foreignKey: 'trainerId',
      as: 'submodules'
    });

    // Trainer can be assigned to many members
    Trainer.belongsToMany(models.Member, {
      through: models.TrainerMember,
      foreignKey: 'trainerId',
      otherKey: 'memberId',
      as: 'members'
    });

    // Trainer has many trainer member assignments
    Trainer.hasMany(models.TrainerMember, {
      foreignKey: 'trainerId',
      as: 'trainerAssignments'
    });

    // Trainer can have many conversations
    Trainer.hasMany(models.TrainerConversation, {
      foreignKey: 'trainerId',
      as: 'conversations'
    });

    // Trainer can have many scores
    Trainer.hasMany(models.TrainerScore, {
      foreignKey: 'trainerId',
      as: 'scores'
    });
  };

  return Trainer;
}; 