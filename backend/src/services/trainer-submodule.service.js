const { Trainer<PERSON><PERSON>module, Trainer, Admin, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

class TrainerSubmoduleService {
  /**
   * Create a new submodule
   * @param {number} trainerId - Trainer ID
   * @param {Object} submoduleData - Submodule data
   * @param {number} adminId - Admin ID creating the submodule
   * @returns {Promise<Object>} - Created submodule result
   */
  async createSubmodule(trainerId, submoduleData, adminId) {
    const transaction = await sequelize.transaction();
    
    try {
      const { name, systemPrompt, description, status = 'active' } = submoduleData;

      // Verify trainer exists and belongs to admin
      const trainer = await Trainer.findOne({
        where: { id: trainerId, adminId }
      });

      if (!trainer) {
        throw new Error('Trainer not found or access denied');
      }

      // Get next order index
      const lastSubmodule = await TrainerSubmodule.findOne({
        where: { trainerId },
        order: [['orderIndex', 'DESC']],
        transaction
      });

      const orderIndex = lastSubmodule ? lastSubmodule.orderIndex + 1 : 0;

      // Validate system prompt for OpenAI compatibility
      const promptValidation = this.validateSystemPrompt(systemPrompt);
      if (!promptValidation.valid) {
        throw new Error(`Invalid system prompt: ${promptValidation.error}`);
      }

      // Create submodule
      const submodule = await TrainerSubmodule.create({
        trainerId,
        name,
        systemPrompt,
        description,
        status,
        orderIndex,
        metadata: {
          createdBy: adminId,
          promptValidation: promptValidation.metadata
        }
      }, { transaction });

      await transaction.commit();

      logger.info(`Submodule created: ${submodule.id} for trainer ${trainerId} by admin ${adminId}`);

      return {
        success: true,
        submodule
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error creating submodule:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get submodules by trainer
   * @param {number} trainerId - Trainer ID
   * @param {number} adminId - Admin ID
   * @returns {Promise<Object>} - Submodules list
   */
  async getSubmodulesByTrainer(trainerId, adminId) {
    try {
      // Verify trainer belongs to admin
      const trainer = await Trainer.findOne({
        where: { id: trainerId, adminId }
      });

      if (!trainer) {
        throw new Error('Trainer not found or access denied');
      }

      const submodules = await TrainerSubmodule.findAll({
        where: { trainerId },
        order: [['orderIndex', 'ASC']],
        include: [
          {
            model: Trainer,
            as: 'trainer',
            attributes: ['id', 'name', 'status']
          }
        ]
      });

      return {
        success: true,
        submodules
      };
    } catch (error) {
      logger.error('Error getting submodules:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update submodule
   * @param {number} submoduleId - Submodule ID
   * @param {Object} updateData - Update data
   * @param {number} adminId - Admin ID updating the submodule
   * @returns {Promise<Object>} - Update result
   */
  async updateSubmodule(submoduleId, updateData, adminId) {
    const transaction = await sequelize.transaction();
    
    try {
      const submodule = await TrainerSubmodule.findOne({
        where: { id: submoduleId },
        include: [
          {
            model: Trainer,
            as: 'trainer',
            where: { adminId }
          }
        ]
      });

      if (!submodule) {
        throw new Error('Submodule not found or access denied');
      }

      // Validate system prompt if it's being updated
      if (updateData.systemPrompt) {
        const promptValidation = this.validateSystemPrompt(updateData.systemPrompt);
        if (!promptValidation.valid) {
          throw new Error(`Invalid system prompt: ${promptValidation.error}`);
        }
        
        // Update metadata with validation info
        updateData.metadata = {
          ...submodule.metadata,
          lastUpdatedBy: adminId,
          promptValidation: promptValidation.metadata
        };
      }

      await submodule.update(updateData, { transaction });
      await transaction.commit();

      logger.info(`Submodule updated: ${submoduleId} by admin ${adminId}`);

      return {
        success: true,
        submodule
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error updating submodule:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete submodule
   * @param {number} submoduleId - Submodule ID
   * @param {number} adminId - Admin ID deleting the submodule
   * @returns {Promise<Object>} - Delete result
   */
  async deleteSubmodule(submoduleId, adminId) {
    const transaction = await sequelize.transaction();
    
    try {
      const submodule = await TrainerSubmodule.findOne({
        where: { id: submoduleId },
        include: [
          {
            model: Trainer,
            as: 'trainer',
            where: { adminId }
          }
        ]
      });

      if (!submodule) {
        throw new Error('Submodule not found or access denied');
      }

      await submodule.destroy({ transaction });
      await transaction.commit();

      logger.info(`Submodule deleted: ${submoduleId} by admin ${adminId}`);

      return {
        success: true,
        message: 'Submodule deleted successfully'
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error deleting submodule:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Reorder submodules
   * @param {number} trainerId - Trainer ID
   * @param {Array} submoduleIds - Array of submodule IDs in new order
   * @param {number} adminId - Admin ID reordering the submodules
   * @returns {Promise<Object>} - Reorder result
   */
  async reorderSubmodules(trainerId, submoduleIds, adminId) {
    const transaction = await sequelize.transaction();
    
    try {
      // Verify trainer belongs to admin
      const trainer = await Trainer.findOne({
        where: { id: trainerId, adminId }
      });

      if (!trainer) {
        throw new Error('Trainer not found or access denied');
      }

      // Verify all submodules belong to the trainer
      const submodules = await TrainerSubmodule.findAll({
        where: { 
          id: { [Op.in]: submoduleIds },
          trainerId 
        },
        transaction
      });

      if (submodules.length !== submoduleIds.length) {
        throw new Error('One or more submodules not found or do not belong to this trainer');
      }

      // Update order indices
      const updatePromises = submoduleIds.map((submoduleId, index) => 
        TrainerSubmodule.update(
          { orderIndex: index },
          { 
            where: { id: submoduleId },
            transaction 
          }
        )
      );

      await Promise.all(updatePromises);
      await transaction.commit();

      logger.info(`Submodules reordered for trainer ${trainerId} by admin ${adminId}`);

      return {
        success: true,
        message: 'Submodules reordered successfully'
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error reordering submodules:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get submodule by ID
   * @param {number} submoduleId - Submodule ID
   * @param {number} adminId - Admin ID
   * @returns {Promise<Object>} - Submodule data
   */
  async getSubmoduleById(submoduleId, adminId) {
    try {
      const submodule = await TrainerSubmodule.findOne({
        where: { id: submoduleId },
        include: [
          {
            model: Trainer,
            as: 'trainer',
            where: { adminId },
            attributes: ['id', 'name', 'status']
          }
        ]
      });

      if (!submodule) {
        throw new Error('Submodule not found or access denied');
      }

      return {
        success: true,
        submodule
      };
    } catch (error) {
      logger.error('Error getting submodule:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate system prompt for OpenAI compatibility
   * @param {string} systemPrompt - System prompt to validate
   * @returns {Object} - Validation result
   */
  validateSystemPrompt(systemPrompt) {
    try {
      // Basic validation
      if (!systemPrompt || systemPrompt.trim().length < 10) {
        return {
          valid: false,
          error: 'System prompt must be at least 10 characters long'
        };
      }

      // Check for required JSON response format instruction
      const hasJsonInstruction = systemPrompt.toLowerCase().includes('json') || 
                                systemPrompt.toLowerCase().includes('response') ||
                                systemPrompt.toLowerCase().includes('format');

      return {
        valid: true,
        metadata: {
          length: systemPrompt.length,
          hasJsonInstruction,
          validatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        valid: false,
        error: 'Error validating system prompt'
      };
    }
  }
}

module.exports = TrainerSubmoduleService; 