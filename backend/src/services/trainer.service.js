const { <PERSON>er, <PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>, Ad<PERSON>, Member, TrainerScore, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const { assignTrainerToMembers, removeTrainerFromMembers } = require('./trainer/assignment.service');

class TrainerService {
  /**
   * Create a new trainer module
   * @param {Object} trainerData - Trainer data
   * @param {number} adminId - Admin ID creating the trainer
   * @returns {Promise<Object>} - Created trainer result
   */
  async createTrainer(trainerData, adminId) {
    const transaction = await sequelize.transaction();
    
    try {
      const { name, systemPrompt, description, status = 'active' } = trainerData;

      // Validate system prompt for OpenAI compatibility
      const promptValidation = this.validateSystemPrompt(systemPrompt);
      if (!promptValidation.valid) {
        throw new Error(`Invalid system prompt: ${promptValidation.error}`);
      }

      // Create trainer
      const trainer = await Trainer.create({
        adminId,
        name,
        systemPrompt,
        description,
        status,
        metadata: {
          createdBy: adminId,
          promptValidation: promptValidation.metadata
        }
      }, { transaction });

      await transaction.commit();

      // Fetch trainer with admin details
      const createdTrainer = await Trainer.findByPk(trainer.id, {
        include: [{
          model: Admin,
          as: 'admin',
          attributes: ['id', 'name', 'email', 'role']
        }]
      });

      logger.info(`Trainer created: ${trainer.id} by admin ${adminId}`);

      return {
        success: true,
        trainer: createdTrainer
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error creating trainer:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get trainer by ID
   * @param {number} trainerId - Trainer ID
   * @param {number} adminId - Admin ID requesting the trainer
   * @returns {Promise<Object>} - Trainer data
   */
  async getTrainerById(trainerId, adminId) {
    try {
      const trainer = await Trainer.findOne({
        where: {
          id: trainerId,
          adminId // Admin can only access their own trainers
        },
        include: [
          {
            model: Admin,
            as: 'admin',
            attributes: ['id', 'name', 'email', 'role']
          },
          {
            model: Member,
            as: 'members',
            attributes: ['id', 'name', 'email'],
            through: {
              attributes: ['created_at'],
              as: 'assignment'
            }
          }
        ]
      });

      if (!trainer) {
        throw new Error('Trainer not found or access denied');
      }

      return {
        success: true,
        trainer
      };
    } catch (error) {
      logger.error('Error getting trainer:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get all trainers for an admin
   * @param {number} adminId - Admin ID
   * @param {Object} options - Query options (pagination, search, status)
   * @returns {Promise<Object>} - Trainers list
   */
  async getTrainersByAdmin(adminId, options = {}) {
    try {
      const { page = 1, limit = 10, search, status } = options;
      const offset = (page - 1) * limit;

      const whereClause = { adminId };
      
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } }
        ];
      }

      if (status) {
        whereClause.status = status;
      }

      const { count, rows: trainers } = await Trainer.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: TrainerSubmodule,
            as: 'submodules',
            attributes: ['id', 'name', 'status', 'orderIndex'],
            order: [['orderIndex', 'ASC']]
          },
          {
            model: Member,
            as: 'members',
            attributes: ['id', 'name', 'email'],
            through: {
              attributes: ['created_at'],
              as: 'assignment'
            }
          }
        ],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      return {
        success: true,
        trainers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting trainers:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update trainer
   * @param {number} trainerId - Trainer ID
   * @param {Object} updateData - Update data
   * @param {number} adminId - Admin ID updating the trainer
   * @returns {Promise<Object>} - Update result
   */
  async updateTrainer(trainerId, updateData, adminId) {
    const transaction = await sequelize.transaction();
    
    try {
      const trainer = await Trainer.findOne({
        where: {
          id: trainerId,
          adminId // Admin can only update their own trainers
        }
      });

      if (!trainer) {
        throw new Error('Trainer not found or access denied');
      }

      // Validate system prompt if it's being updated
      if (updateData.systemPrompt) {
        const promptValidation = this.validateSystemPrompt(updateData.systemPrompt);
        if (!promptValidation.valid) {
          throw new Error(`Invalid system prompt: ${promptValidation.error}`);
        }
        
        // Update metadata with validation info
        updateData.metadata = {
          ...trainer.metadata,
          lastUpdatedBy: adminId,
          promptValidation: promptValidation.metadata
        };
      }

      await trainer.update(updateData, { transaction });
      await transaction.commit();

      // Fetch updated trainer with associations
      const updatedTrainer = await Trainer.findByPk(trainerId, {
        include: [
          {
            model: Admin,
            as: 'admin',
            attributes: ['id', 'name', 'email', 'role']
          },
          {
            model: Member,
            as: 'members',
            attributes: ['id', 'name', 'email'],
            through: {
              attributes: ['created_at'],
              as: 'assignment'
            }
          }
        ]
      });

      logger.info(`Trainer updated: ${trainerId} by admin ${adminId}`);

      return {
        success: true,
        trainer: updatedTrainer
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error updating trainer:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete trainer
   * @param {number} trainerId - Trainer ID
   * @param {number} adminId - Admin ID deleting the trainer
   * @returns {Promise<Object>} - Delete result
   */
  async deleteTrainer(trainerId, adminId) {
    const transaction = await sequelize.transaction();
    
    try {
      const trainer = await Trainer.findOne({
        where: {
          id: trainerId,
          adminId // Admin can only delete their own trainers
        }
      });

      if (!trainer) {
        throw new Error('Trainer not found or access denied');
      }

      await trainer.destroy({ transaction });
      await transaction.commit();

      logger.info(`Trainer deleted: ${trainerId} by admin ${adminId}`);

      return {
        success: true,
        message: 'Trainer deleted successfully'
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error deleting trainer:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Assign trainer to members
   * @param {number} trainerId - Trainer ID
   * @param {Array} memberIds - Array of member IDs
   * @param {number} adminId - Admin ID making the assignment
   * @returns {Promise<Object>} - Assignment result
   */
  async assignTrainerToMembers(trainerId, memberIds, adminId) {
    return assignTrainerToMembers(trainerId, memberIds, adminId);
  }

  /**
   * Remove trainer assignment from members
   * @param {number} trainerId - Trainer ID
   * @param {Array} memberIds - Array of member IDs
   * @param {number} adminId - Admin ID removing the assignment
   * @returns {Promise<Object>} - Removal result
   */
  async removeTrainerFromMembers(trainerId, memberIds, adminId) {
    return removeTrainerFromMembers(trainerId, memberIds, adminId);
  }

  /**
   * Get trainers assigned to a member
   * @param {number} memberId - Member ID
   * @returns {Promise<Object>} - Assigned trainers
   */
  async getTrainersByMember(memberId) {
    try {
      const member = await Member.findByPk(memberId, {
        include: [
          {
            model: Trainer,
            as: 'trainers',
            where: { status: 'active' },
            required: false,
            include: [
              {
                model: Admin,
                as: 'admin',
                attributes: ['id', 'name', 'email']
              }
            ],
            through: {
              attributes: ['created_at'],
              as: 'assignment'
            }
          }
        ]
      });

      if (!member) {
        throw new Error('Member not found');
      }

      return {
        success: true,
        trainers: member.trainers || []
      };
    } catch (error) {
      logger.error('Error getting trainers for member:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate system prompt for OpenAI compatibility
   * @param {string} systemPrompt - System prompt to validate
   * @returns {Object} - Validation result
   */
  validateSystemPrompt(systemPrompt) {
    try {
      // Basic validation rules
      const validationRules = {
        minLength: 10,
        requiredElements: ['You are', 'training', 'progress'],
        forbiddenElements: ['<script>', '<html>', 'javascript:', 'eval(']
      };

      const result = {
        valid: true,
        error: null,
        metadata: {
          length: systemPrompt.length,
          validatedAt: new Date().toISOString()
        }
      };

      // Length validation
      if (systemPrompt.length < validationRules.minLength) {
        result.valid = false;
        result.error = `System prompt must be at least ${validationRules.minLength} characters long`;
        return result;
      }

      // Check for forbidden elements
      const lowerPrompt = systemPrompt.toLowerCase();
      for (const forbidden of validationRules.forbiddenElements) {
        if (lowerPrompt.includes(forbidden.toLowerCase())) {
          result.valid = false;
          result.error = `System prompt contains forbidden element: ${forbidden}`;
          return result;
        }
      }

      // Check for recommended elements (warnings, not errors)
      const warnings = [];
      for (const required of validationRules.requiredElements) {
        if (!lowerPrompt.includes(required.toLowerCase())) {
          warnings.push(`Consider including "${required}" in your system prompt`);
        }
      }

      result.metadata.warnings = warnings;
      result.metadata.score = this.calculatePromptScore(systemPrompt);

      return result;
    } catch (error) {
      logger.error('Error validating system prompt:', error);
      return {
        valid: false,
        error: 'System prompt validation failed',
        metadata: {}
      };
    }
  }

  /**
   * Calculate a quality score for the system prompt
   * @param {string} systemPrompt - System prompt to score
   * @returns {number} - Score from 0-100
   */
  calculatePromptScore(systemPrompt) {
    let score = 50; // Base score

    // Length scoring
    if (systemPrompt.length >= 100 && systemPrompt.length <= 1000) {
      score += 20;
    } else if (systemPrompt.length >= 50) {
      score += 10;
    }

    // Structure scoring
    if (systemPrompt.includes('You are') || systemPrompt.includes('You will')) {
      score += 15;
    }

    if (systemPrompt.includes('progress') || systemPrompt.includes('score')) {
      score += 15;
    }

    // Ensure score is within bounds
    return Math.min(100, Math.max(0, score));
  }

  /**
   * Get trainer statistics for admin dashboard
   * @param {number} adminId - Admin ID
   * @returns {Promise<Object>} - Trainer statistics
   */
  async getTrainerStatistics(adminId) {
    try {
      const stats = await Trainer.findAll({
        where: { adminId },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      const memberAssignments = await sequelize.query(`
        SELECT COUNT(DISTINCT tm.member_id) as total_members
        FROM trainer_member tm
        JOIN trainers t ON tm.trainer_id = t.id
        WHERE t.adminId = :adminId
      `, {
        replacements: { adminId },
        type: sequelize.QueryTypes.SELECT
      });

      return {
        success: true,
        statistics: {
          trainersByStatus: stats.reduce((acc, stat) => {
            acc[stat.status] = parseInt(stat.count);
            return acc;
          }, { active: 0, inactive: 0 }),
          totalMembers: memberAssignments[0]?.total_members || 0
        }
      };
    } catch (error) {
      logger.error('Error getting trainer statistics:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = TrainerService; 