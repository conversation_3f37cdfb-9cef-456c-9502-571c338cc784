{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/hooks/useDashboard.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useAuthStore } from '@/store/auth';\nimport { api } from '@/lib/api';\nimport { DashboardStats, SuperAdminDashboardStats } from '@/types';\n\nexport function useDashboard() {\n  const { user } = useAuthStore();\n  const [stats, setStats] = useState<DashboardStats | SuperAdminDashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  const loadDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = user?.role === 'super_admin' \n        ? await api.monitoring.getSuperAdminDashboard()\n        : await api.monitoring.getDashboard();\n      \n      setStats(response.data.data);\n    } catch (err) {\n      setError('Failed to load dashboard data');\n      console.error('Dashboard error:', err);\n    } finally {\n      setLoading(false);\n    }\n  }, [user]);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [loadDashboardData]);\n\n  return {\n    user,\n    stats,\n    loading,\n    error,\n    isSuperAdmin: user?.role === 'super_admin',\n    loadDashboardData,\n  };\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;AAGO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoD;IACrF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YACpC,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,gBAC5B,MAAM,oHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,sBAAsB,KAC3C,MAAM,oHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,YAAY;gBAErC,SAAS,SAAS,IAAI,CAAC,IAAI;YAC7B,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,QAAQ,KAAK,CAAC,oBAAoB;YACpC,SAAU;gBACR,WAAW;YACb;QACF;sDAAG;QAAC;KAAK;IAET,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAkB;IAEtB,OAAO;QACL;QACA;QACA;QACA;QACA,cAAc,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QAC7B;IACF;AACF;GAlCgB;;QACG,uHAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/dashboard/DashboardHeader.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface DashboardHeaderProps {\n  isSuperAdmin: boolean;\n  userName: string | undefined;\n}\n\nexport function DashboardHeader({ isSuperAdmin, userName }: DashboardHeaderProps) {\n  return (\n    <div className=\"mb-6\">\n      <h1 className=\"text-2xl font-bold text-gray-900\">\n        {isSuperAdmin ? 'Super Admin Dashboard' : 'Admin Dashboard'}\n      </h1>\n      <p className=\"text-gray-600\">\n        Welcome back, {userName}! Here&apos;s what&apos;s happening with your platform.\n      </p>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;;AAOO,SAAS,gBAAgB,KAAgD;QAAhD,EAAE,YAAY,EAAE,QAAQ,EAAwB,GAAhD;IAC9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BACX,eAAe,0BAA0B;;;;;;0BAE5C,6LAAC;gBAAE,WAAU;;oBAAgB;oBACZ;oBAAS;;;;;;;;;;;;;AAIhC;KAXgB", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format date ke string (misal: Jan 1, 2024)\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });\n}\n\n// Format waktu (misal: 14:30)\nexport function formatTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });\n}\n\n// Format waktu relatif (misal: 2 hours ago)\nexport function formatRelativeTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diff = (now.getTime() - d.getTime()) / 1000;\n  if (diff < 60) return `${Math.floor(diff)} seconds ago`;\n  if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;\n  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;\n  return `${Math.floor(diff / 86400)} days ago`;\n}\n\n// Format angka (misal: 1,234)\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('en-US');\n}\n\n// Status color untuk badge\nexport function getStatusColor(status: string): string {\n  switch (status) {\n    case 'active': return 'bg-green-100 text-green-800';\n    case 'inactive': return 'bg-gray-100 text-gray-800';\n    case 'draft': return 'bg-yellow-100 text-yellow-800';\n    case 'completed': return 'bg-blue-100 text-blue-800';\n    default: return 'bg-gray-100 text-gray-800';\n  }\n}\n\n// Ekstrak pesan error dari objek error\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message;\n  if (typeof error === 'string') return error;\n  return 'Unknown error';\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QAAE,MAAM;QAAW,OAAO;QAAS,KAAK;IAAU;AACzF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QAAE,MAAM;QAAW,QAAQ;IAAU;AAC5E;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAC7C,IAAI,OAAO,IAAI,OAAO,AAAC,GAAmB,OAAjB,KAAK,KAAK,CAAC,OAAM;IAC1C,IAAI,OAAO,MAAM,OAAO,AAAC,GAAwB,OAAtB,KAAK,KAAK,CAAC,OAAO,KAAI;IACjD,IAAI,OAAO,OAAO,OAAO,AAAC,GAA0B,OAAxB,KAAK,KAAK,CAAC,OAAO,OAAM;IACpD,OAAO,AAAC,GAA2B,OAAzB,KAAK,KAAK,CAAC,OAAO,QAAO;AACrC;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAa,OAAO;QACzB;YAAS,OAAO;IAClB;AACF;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('rounded-lg border bg-card text-card-foreground shadow-sm', className)}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('text-2xl font-semibold leading-none tracking-tight', className)}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4DAA4D;QACzE,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/dashboard/StatCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { formatNumber } from '@/lib/utils';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\n\ninterface StatCardProps {\n  title: string;\n  value: number;\n  icon: React.ComponentType<{ className?: string }>;\n  color: string;\n}\n\nexport function StatCard({ title, value, icon: Icon, color }: StatCardProps) {\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center\">\n          <div className={`${color} rounded-md p-3`}>\n            <Icon className=\"h-6 w-6 text-white\" />\n          </div>\n          <div className=\"ml-4\">\n            <CardTitle>{title}</CardTitle>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        <p className=\"text-2xl font-semibold text-gray-900\">{formatNumber(value)}</p>\n      </CardContent>\n    </Card>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;;;;AASO,SAAS,SAAS,KAAkD;QAAlD,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,KAAK,EAAiB,GAAlD;IACvB,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,AAAC,GAAQ,OAAN,OAAM;sCACvB,cAAA,6LAAC;gCAAK,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAE;;;;;;;;;;;;;;;;;;;;;;0BAIlB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAE,WAAU;8BAAwC,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;;;;;;;AAI1E;KAlBgB", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/dashboard/DashboardStatsGrid.tsx"], "sourcesContent": ["import React from 'react';\nimport { StatCard } from './StatCard';\nimport { DashboardStats, SuperAdminDashboardStats } from '@/types';\nimport { BookOpen, Users, MessageSquare, Activity } from 'lucide-react';\n\ninterface DashboardStatsGridProps {\n  isSuperAdmin: boolean;\n  stats: DashboardStats | SuperAdminDashboardStats | null;\n}\n\nexport function DashboardStatsGrid({ isSuperAdmin, stats }: DashboardStatsGridProps) {\n  if (!stats) {\n    return null;\n  }\n\n  const adminStats = stats as DashboardStats;\n  const superAdminStats = stats as SuperAdminDashboardStats;\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n      {isSuperAdmin ? (\n        <>\n          <StatCard\n            title=\"Total Admins\"\n            value={superAdminStats.systemStats.totalAdmins}\n            icon={Users}\n            color=\"bg-blue-500\"\n          />\n          <StatCard\n            title=\"Total Members\"\n            value={superAdminStats.systemStats.totalMembers}\n            icon={Users}\n            color=\"bg-green-500\"\n          />\n          <StatCard\n            title=\"Total Trainers\"\n            value={superAdminStats.systemStats.totalTrainers}\n            icon={BookOpen}\n            color=\"bg-purple-500\"\n          />\n          <StatCard\n            title=\"Total Conversations\"\n            value={superAdminStats.systemStats.totalConversations}\n            icon={MessageSquare}\n            color=\"bg-orange-500\"\n          />\n        </>\n      ) : (\n        <>\n          <StatCard\n            title=\"Total Trainers\"\n            value={adminStats.totalTrainers}\n            icon={BookOpen}\n            color=\"bg-blue-500\"\n          />\n          <StatCard\n            title=\"Active Trainers\"\n            value={adminStats.activeTrainers}\n            icon={Activity}\n            color=\"bg-green-500\"\n          />\n          <StatCard\n            title=\"Total Members\"\n            value={adminStats.totalMembers}\n            icon={Users}\n            color=\"bg-purple-500\"\n          />\n          <StatCard\n            title=\"Conversations\"\n            value={adminStats.totalConversations}\n            icon={MessageSquare}\n            color=\"bg-orange-500\"\n          />\n        </>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAEA;AAAA;AAAA;AAAA;;;;AAOO,SAAS,mBAAmB,KAAgD;QAAhD,EAAE,YAAY,EAAE,KAAK,EAA2B,GAAhD;IACjC,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,aAAa;IACnB,MAAM,kBAAkB;IAExB,qBACE,6LAAC;QAAI,WAAU;kBACZ,6BACC;;8BACE,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,gBAAgB,WAAW,CAAC,WAAW;oBAC9C,MAAM,uMAAA,CAAA,QAAK;oBACX,OAAM;;;;;;8BAER,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,gBAAgB,WAAW,CAAC,YAAY;oBAC/C,MAAM,uMAAA,CAAA,QAAK;oBACX,OAAM;;;;;;8BAER,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,gBAAgB,WAAW,CAAC,aAAa;oBAChD,MAAM,iNAAA,CAAA,WAAQ;oBACd,OAAM;;;;;;8BAER,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,gBAAgB,WAAW,CAAC,kBAAkB;oBACrD,MAAM,2NAAA,CAAA,gBAAa;oBACnB,OAAM;;;;;;;yCAIV;;8BACE,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,WAAW,aAAa;oBAC/B,MAAM,iNAAA,CAAA,WAAQ;oBACd,OAAM;;;;;;8BAER,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,WAAW,cAAc;oBAChC,MAAM,6MAAA,CAAA,WAAQ;oBACd,OAAM;;;;;;8BAER,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,WAAW,YAAY;oBAC9B,MAAM,uMAAA,CAAA,QAAK;oBACX,OAAM;;;;;;8BAER,6LAAC,8IAAA,CAAA,WAAQ;oBACP,OAAM;oBACN,OAAO,WAAW,kBAAkB;oBACpC,MAAM,2NAAA,CAAA,gBAAa;oBACnB,OAAM;;;;;;;;;;;;;AAMlB;KAnEgB", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/dashboard/RecentActivity.tsx"], "sourcesContent": ["import React from 'react';\nimport { Clock, MessageSquare } from 'lucide-react';\nimport { Conversation } from '@/types';\nimport { formatRelativeTime } from '@/lib/utils';\n\ninterface RecentActivityProps {\n  activities: Conversation[];\n}\n\nexport function RecentActivity({ activities }: RecentActivityProps) {\n  if (activities.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h2 className=\"text-lg font-medium text-gray-900\">Recent Activity</h2>\n        </div>\n        <div className=\"px-6 py-8 text-center text-gray-500\">\n          No recent activity found\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow\">\n      <div className=\"px-6 py-4 border-b border-gray-200\">\n        <h2 className=\"text-lg font-medium text-gray-900\">Recent Activity</h2>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {activities.slice(0, 10).map((conversation, index) => (\n          <div key={index} className=\"px-6 py-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center\">\n                  <MessageSquare className=\"h-4 w-4 text-gray-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">\n                    {conversation.member?.name || 'Unknown Member'}\n                  </p>\n                  <p className=\"text-sm text-gray-500\">\n                    {conversation.trainer?.name || 'Unknown Trainer'}\n                  </p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-sm text-gray-500\">\n                  {formatRelativeTime(conversation.created_at)}\n                </p>\n                <div className=\"flex items-center space-x-1 text-xs text-gray-400\">\n                  <Clock className=\"h-3 w-3\" />\n                  <span>{conversation.messageFrom === 'ai' ? 'AI Response' : 'Member Message'}</span>\n                </div>\n              </div>\n            </div>\n            <div className=\"mt-2\">\n              <p className=\"text-sm text-gray-600 truncate\">\n                {conversation.message}\n              </p>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAAA;AAEA;;;;AAMO,SAAS,eAAe,KAAmC;QAAnC,EAAE,UAAU,EAAuB,GAAnC;IAC7B,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;;;;;;8BAEpD,6LAAC;oBAAI,WAAU;8BAAsC;;;;;;;;;;;;IAK3D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAoC;;;;;;;;;;;0BAEpD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,cAAc;wBAS/B,sBAGA;yCAXX,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEACV,EAAA,uBAAA,aAAa,MAAM,cAAnB,2CAAA,qBAAqB,IAAI,KAAI;;;;;;kEAEhC,6LAAC;wDAAE,WAAU;kEACV,EAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,IAAI,KAAI;;;;;;;;;;;;;;;;;;kDAIrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,UAAU;;;;;;0DAE7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAM,aAAa,WAAW,KAAK,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAIjE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CACV,aAAa,OAAO;;;;;;;;;;;;uBA3BjB;;;;;;;;;;;;;;;;;AAmCpB;KAxDgB", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/dashboard/QuickActionCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';\n\ninterface QuickActionCardProps {\n  title: string;\n  description: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n}\n\nexport function QuickActionCard({ title, description, href, icon: Icon }: QuickActionCardProps) {\n  return (\n    <a href={href} className=\"block hover:shadow-md transition-shadow\">\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center\">\n            <div className=\"bg-indigo-100 rounded-md p-3\">\n              <Icon className=\"h-6 w-6 text-indigo-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <CardTitle>{title}</CardTitle>\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-sm text-gray-600\">{description}</p>\n        </CardContent>\n      </Card>\n    </a>\n  );\n}"], "names": [], "mappings": ";;;;AACA;;;AASO,SAAS,gBAAgB,KAA8D;QAA9D,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,IAAI,EAAwB,GAA9D;IAC9B,qBACE,6LAAC;QAAE,MAAM;QAAM,WAAU;kBACvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAE;;;;;;;;;;;;;;;;;;;;;;8BAIlB,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;;;;;AAKhD;KApBgB", "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { BookOpen, Users, MessageSquare } from 'lucide-react';\nimport { useDashboard } from '@/hooks/useDashboard';\nimport { DashboardHeader } from '@/components/dashboard/DashboardHeader';\nimport { DashboardStatsGrid } from '@/components/dashboard/DashboardStatsGrid';\nimport { RecentActivity } from '@/components/dashboard/RecentActivity';\nimport { QuickActionCard } from '@/components/dashboard/QuickActionCard';\nimport { DashboardStats, SuperAdminDashboardStats } from '@/types';\n\nexport default function DashboardPage() {\n  const { user, stats, loading, error, isSuperAdmin, loadDashboardData } = useDashboard();\n\n  if (loading) {\n    return (\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {[1, 2, 3, 4].map((i) => (\n              <div key={i} className=\"bg-white p-6 rounded-lg shadow\">\n                <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-1/3\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\nif (error) {\n    return (\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"text-red-700\">{error}</div>\n          <button\n            onClick={loadDashboardData}\n            className=\"mt-2 text-red-600 hover:text-red-500 text-sm underline\"\n          >\n            Try again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const adminStats = stats as DashboardStats;\n  const superAdminStats = stats as SuperAdminDashboardStats;\n  const recentActivity = isSuperAdmin ? superAdminStats?.recentActivity : adminStats?.recentConversations;\n\n  return (\n    <div className=\"px-4 sm:px-6 lg:px-8\">\n      <DashboardHeader isSuperAdmin={isSuperAdmin} userName={user?.name} />\n      <DashboardStatsGrid isSuperAdmin={isSuperAdmin} stats={stats} />\n      <RecentActivity activities={recentActivity || []} />\n\n      {/* Quick Actions */}\n      <div className=\"mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <QuickActionCard\n          title=\"Create New Trainer\"\n          description=\"Set up a new AI trainer module\"\n          href=\"/admin/dashboard/trainers/create\"\n          icon={BookOpen}\n        />\n        <QuickActionCard\n          title=\"Manage Members\"\n          description=\"View and manage platform members\"\n          href=\"/admin/dashboard/members\"\n          icon={Users}\n        />\n        <QuickActionCard\n          title=\"Monitor Conversations\"\n          description=\"View live conversation monitoring\"\n          href=\"/admin/dashboard/monitoring\"\n          icon={MessageSquare}\n        />\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAWe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAEpF,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;;;;;;;;;;;;IAStB;IAEF,IAAI,OAAO;QACP,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,aAAa;IACnB,MAAM,kBAAkB;IACxB,MAAM,iBAAiB,eAAe,4BAAA,sCAAA,gBAAiB,cAAc,GAAG,uBAAA,iCAAA,WAAY,mBAAmB;IAEvG,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qJAAA,CAAA,kBAAe;gBAAC,cAAc;gBAAc,QAAQ,EAAE,iBAAA,2BAAA,KAAM,IAAI;;;;;;0BACjE,6LAAC,wJAAA,CAAA,qBAAkB;gBAAC,cAAc;gBAAc,OAAO;;;;;;0BACvD,6LAAC,oJAAA,CAAA,iBAAc;gBAAC,YAAY,kBAAkB,EAAE;;;;;;0BAGhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qJAAA,CAAA,kBAAe;wBACd,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,MAAM,iNAAA,CAAA,WAAQ;;;;;;kCAEhB,6LAAC,qJAAA,CAAA,kBAAe;wBACd,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,MAAM,uMAAA,CAAA,QAAK;;;;;;kCAEb,6LAAC,qJAAA,CAAA,kBAAe;wBACd,OAAM;wBACN,aAAY;wBACZ,MAAK;wBACL,MAAM,2NAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;AAK7B;GAtEwB;;QACmD,+HAAA,CAAA,eAAY;;;KAD/D", "debugId": null}}]}