{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format date ke string (misal: Jan 1, 2024)\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });\n}\n\n// Format waktu (misal: 14:30)\nexport function formatTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });\n}\n\n// Format waktu relatif (misal: 2 hours ago)\nexport function formatRelativeTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diff = (now.getTime() - d.getTime()) / 1000;\n  if (diff < 60) return `${Math.floor(diff)} seconds ago`;\n  if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;\n  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;\n  return `${Math.floor(diff / 86400)} days ago`;\n}\n\n// Format angka (misal: 1,234)\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('en-US');\n}\n\n// Status color untuk badge\nexport function getStatusColor(status: string): string {\n  switch (status) {\n    case 'active': return 'bg-green-100 text-green-800';\n    case 'inactive': return 'bg-gray-100 text-gray-800';\n    case 'draft': return 'bg-yellow-100 text-yellow-800';\n    case 'completed': return 'bg-blue-100 text-blue-800';\n    default: return 'bg-gray-100 text-gray-800';\n  }\n}\n\n// Ekstrak pesan error dari objek error\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message;\n  if (typeof error === 'string') return error;\n  return 'Unknown error';\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QAAE,MAAM;QAAW,OAAO;QAAS,KAAK;IAAU;AACzF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QAAE,MAAM;QAAW,QAAQ;IAAU;AAC5E;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAC7C,IAAI,OAAO,IAAI,OAAO,AAAC,GAAmB,OAAjB,KAAK,KAAK,CAAC,OAAM;IAC1C,IAAI,OAAO,MAAM,OAAO,AAAC,GAAwB,OAAtB,KAAK,KAAK,CAAC,OAAO,KAAI;IACjD,IAAI,OAAO,OAAO,OAAO,AAAC,GAA0B,OAAxB,KAAK,KAAK,CAAC,OAAO,OAAM;IACpD,OAAO,AAAC,GAA2B,OAAzB,KAAK,KAAK,CAAC,OAAO,QAAO;AACrC;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAa,OAAO;QACzB;YAAS,OAAO;IAClB;AACF;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  children,\n  disabled,\n  ...props\n}) => {\n  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n  \n  const variants = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700',\n    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',\n    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50',\n    ghost: 'text-gray-600 hover:bg-gray-100',\n    destructive: 'bg-red-600 text-white hover:bg-red-700'\n  };\n\n  const sizes = {\n    sm: 'h-8 px-3 text-sm',\n    md: 'h-10 px-4 text-sm',\n    lg: 'h-12 px-6 text-base'\n  };\n\n  return (\n    <button\n      className={cn(\n        baseStyles,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button; "], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,SAAgC;QAAC,EACrC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;KA7CM;uCA+CS", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input: React.FC<InputProps> = ({\n  label,\n  error,\n  helperText,\n  leftIcon,\n  rightIcon,\n  className,\n  ...props\n}) => {\n  const inputStyles = cn(\n    'block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm',\n    error && 'border-red-300 focus:border-red-500 focus:ring-red-500',\n    leftIcon && 'pl-10',\n    rightIcon && 'pr-10',\n    className\n  );\n\n  return (\n    <div className=\"space-y-1\">\n      {label && (\n        <label className=\"block text-sm font-medium text-gray-700\">\n          {label}\n        </label>\n      )}\n      <div className=\"relative\">\n        {leftIcon && (\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <div className=\"h-5 w-5 text-gray-400\">\n              {leftIcon}\n            </div>\n          </div>\n        )}\n        <input\n          className={inputStyles}\n          {...props}\n        />\n        {rightIcon && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n            <div className=\"h-5 w-5 text-gray-400\">\n              {rightIcon}\n            </div>\n          </div>\n        )}\n      </div>\n      {error && (\n        <p className=\"text-sm text-red-600\">{error}</p>\n      )}\n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500\">{helperText}</p>\n      )}\n    </div>\n  );\n};\n\nexport default Input; "], "names": [], "mappings": ";;;;AACA;;;AAUA,MAAM,QAA8B;QAAC,EACnC,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,SAAS,EACT,GAAG,OACJ;IACC,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,0GACA,SAAS,0DACT,YAAY,SACZ,aAAa,SACb;IAGF,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;kCAIP,6LAAC;wBACC,WAAW;wBACV,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;YAKR,uBACC,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;YAEtC,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAI9C;KApDM;uCAsDS", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/app/admin/dashboard/trainers/create/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { <PERSON>Left, Save, Eye, EyeOff } from 'lucide-react';\nimport Button from '@/components/ui/Button';\nimport Input from '@/components/ui/Input';\nimport { api } from '@/lib/api';\nimport Link from 'next/link';\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\n\ninterface TrainerFormData {\n  name: string;\n  systemPrompt: string;\n  description: string;\n  status: 'active' | 'inactive' | 'draft';\n}\n\nconst CreateTrainerPage: React.FC = () => {\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n  const [showPrompt, setShowPrompt] = useState(false);\n  const [formData, setFormData] = useState<TrainerFormData>({\n    name: '',\n    systemPrompt: '',\n    description: '',\n    status: 'draft'\n  });\n  const [errors, setErrors] = useState<Partial<TrainerFormData>>({});\n\n  const handleInputChange = (field: keyof TrainerFormData, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Partial<TrainerFormData> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length < 3) {\n      newErrors.name = 'Name must be at least 3 characters';\n    }\n\n    if (!formData.systemPrompt.trim()) {\n      newErrors.systemPrompt = 'System prompt is required';\n    } else if (formData.systemPrompt.length < 10) {\n      newErrors.systemPrompt = 'System prompt must be at least 10 characters';\n    }\n\n    if (formData.description && formData.description.length > 500) {\n      newErrors.description = 'Description must be less than 500 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      await api.trainers.create(formData);\n      router.push('/admin/dashboard/trainers');\n    } catch (error: unknown) {\n      console.error('Error creating trainer:', error);\n      // Handle specific API errors\n      if (error instanceof Error) {\n        // A more robust error handling could be implemented here\n        // For now, just log the message\n        console.error(error.message);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Link href=\"/admin/dashboard/trainers\">\n            <Button variant=\"ghost\" size=\"sm\">\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Trainers\n            </Button>\n          </Link>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Create New Trainer</h1>\n            <p className=\"text-gray-600\">Set up a new AI trainer with custom configuration</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Form */}\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-6\">Basic Information</h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"md:col-span-2\">\n              <Input\n                label=\"Trainer Name\"\n                placeholder=\"Enter trainer name\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                error={errors.name}\n                helperText=\"A descriptive name for your AI trainer\"\n              />\n            </div>\n\n            <div className=\"md:col-span-2\">\n              <Input\n                label=\"Description\"\n                placeholder=\"Enter trainer description (optional)\"\n                value={formData.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                error={errors.description}\n                helperText=\"Brief description of what this trainer does\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Status\n              </label>\n              <Select\n                value={formData.status}\n                onValueChange={(value) => handleInputChange('status', value)}\n              >\n                <SelectTrigger className=\"w-full\">\n                  <SelectValue placeholder=\"Status\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"draft\">Draft</SelectItem>\n                  <SelectItem value=\"active\">Active</SelectItem>\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\n                </SelectContent>\n              </Select>\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Draft trainers are not available to members\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">System Prompt</h2>\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setShowPrompt(!showPrompt)}\n            >\n              {showPrompt ? <EyeOff className=\"h-4 w-4 mr-2\" /> : <Eye className=\"h-4 w-4 mr-2\" />}\n              {showPrompt ? 'Hide' : 'Show'} Prompt\n            </Button>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                System Prompt\n              </label>\n              <textarea\n                value={formData.systemPrompt}\n                onChange={(e) => handleInputChange('systemPrompt', e.target.value)}\n                placeholder=\"Enter the system prompt that defines how this AI trainer should behave...\"\n                rows={showPrompt ? 12 : 6}\n                className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-mono ${\n                  !showPrompt ? 'text-transparent bg-gray-100' : ''\n                }`}\n                style={!showPrompt ? {\n                  color: 'transparent',\n                  textShadow: '0 0 5px rgba(0,0,0,0.5)',\n                  backgroundColor: '#f9fafb'\n                } : {}}\n              />\n              {errors.systemPrompt && (\n                <p className=\"text-sm text-red-600 mt-1\">{errors.systemPrompt}</p>\n              )}\n              <p className=\"text-sm text-gray-500 mt-1\">\n                This prompt defines the AI trainer&apos;s personality, knowledge, and behavior patterns\n              </p>\n            </div>\n\n            <div className=\"bg-blue-50 p-4 rounded-md\">\n              <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Prompt Guidelines:</h3>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• Be specific about the trainer&apos;s role and expertise</li>\n                <li>• Define the communication style and tone</li>\n                <li>• Include any specific instructions or constraints</li>\n                <li>• Consider the target audience (members)</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex justify-end space-x-4\">\n          <Link href=\"/admin/dashboard/trainers\">\n            <Button variant=\"outline\" type=\"button\">\n              Cancel\n            </Button>\n          </Link>\n          <Button type=\"submit\" loading={loading}>\n            <Save className=\"h-4 w-4 mr-2\" />\n            Create Trainer\n          </Button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default CreateTrainerPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAwBA,MAAM,oBAA8B;;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,MAAM;QACN,cAAc;QACd,aAAa;QACb,QAAQ;IACV;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IAEhE,MAAM,oBAAoB,CAAC,OAA8B;QACvD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAsC,CAAC;QAE7C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;YACnC,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,UAAU,YAAY,GAAG;QAC3B,OAAO,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,IAAI;YAC5C,UAAU,YAAY,GAAG;QAC3B;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,oHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,6BAA6B;YAC7B,IAAI,iBAAiB,OAAO;gBAC1B,yDAAyD;gBACzD,gCAAgC;gBAChC,QAAQ,KAAK,CAAC,MAAM,OAAO;YAC7B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAQ,MAAK;;kDAC3B,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI1C,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAMnC,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAK;4CACJ,OAAM;4CACN,aAAY;4CACZ,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACzD,OAAO,OAAO,IAAI;4CAClB,YAAW;;;;;;;;;;;kDAIf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAK;4CACJ,OAAM;4CACN,aAAY;4CACZ,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,OAAO,OAAO,WAAW;4CACzB,YAAW;;;;;;;;;;;kDAIf,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,MAAM;gDACtB,eAAe,CAAC,QAAU,kBAAkB,UAAU;;kEAEtD,6LAAC,qIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;;;;;;;;;;;;;0DAGjC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAOhD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC,qIAAA,CAAA,UAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc,CAAC;;4CAE7B,2BAAa,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;yGAAoB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAClE,aAAa,SAAS;4CAAO;;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACjE,aAAY;gDACZ,MAAM,aAAa,KAAK;gDACxB,WAAW,AAAC,oHAEX,OADC,CAAC,aAAa,iCAAiC;gDAEjD,OAAO,CAAC,aAAa;oDACnB,OAAO;oDACP,YAAY;oDACZ,iBAAiB;gDACnB,IAAI,CAAC;;;;;;4CAEN,OAAO,YAAY,kBAClB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,YAAY;;;;;;0DAE/D,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAK5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAS;;;;;;;;;;;0CAI1C,6LAAC,qIAAA,CAAA,UAAM;gCAAC,MAAK;gCAAS,SAAS;;kDAC7B,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;GA7MM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCA+MS", "debugId": null}}]}