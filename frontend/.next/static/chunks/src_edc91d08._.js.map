{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/hooks/useTrainers.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { api } from '@/lib/api';\nimport { Trainer } from '@/types';\n\nexport const useTrainers = () => {\n  const [trainers, setTrainers] = useState<Trainer[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [deleteModal, setDeleteModal] = useState({ isOpen: false, trainer: null as Trainer | null });\n  const [sortKey, setSortKey] = useState<keyof Trainer>('name');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');\n\n  const fetchTrainers = async () => {\n    try {\n      setLoading(true);\n      const response = await api.trainers.getAll();\n      // Pastikan trainers selalu array\n      const trainersData = response.data?.trainers;\n      setTrainers(Array.isArray(trainersData) ? trainersData : []);\n    } catch (error) {\n      console.error('Error fetching trainers:', error);\n      setTrainers([]); // fallback jika error\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchTrainers();\n  }, []);\n\n  const handleSort = (key: keyof Trainer) => {\n    if (sortKey === key) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortKey(key);\n      setSortDirection('asc');\n    }\n  };\n\n  const handleDelete = async () => {\n    if (!deleteModal.trainer) return;\n    try {\n      await api.trainers.delete(deleteModal.trainer.id);\n      setTrainers(trainers.filter(t => t.id !== deleteModal.trainer!.id));\n      closeDeleteModal();\n    } catch (error) {\n      console.error('Error deleting trainer:', error);\n    }\n  };\n\n  const sortedTrainers = useMemo(() => {\n    return [...trainers].sort((a, b) => {\n      const aValue = a[sortKey];\n      const bValue = b[sortKey];\n      \n      if (aValue == null) return 1;\n      if (bValue == null) return -1;\n\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;\n      return 0;\n    });\n  }, [trainers, sortKey, sortDirection]);\n\n  const openDeleteModal = (trainer: Trainer) => setDeleteModal({ isOpen: true, trainer });\n  const closeDeleteModal = () => setDeleteModal({ isOpen: false, trainer: null });\n\n  return {\n    trainers,\n    loading,\n    deleteModal,\n    sortKey,\n    sortDirection,\n    handleSort,\n    handleDelete,\n    sortedTrainers,\n    openDeleteModal,\n    closeDeleteModal,\n  };\n};"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAMO,MAAM,cAAc;;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,QAAQ;QAAO,SAAS;IAAuB;IAChG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,MAAM,gBAAgB;QACpB,IAAI;gBAImB;YAHrB,WAAW;YACX,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM;YAC1C,iCAAiC;YACjC,MAAM,gBAAe,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,QAAQ;YAC5C,YAAY,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;QAC7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY,EAAE,GAAG,sBAAsB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,IAAI,YAAY,KAAK;YACnB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,WAAW;YACX,iBAAiB;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY,OAAO,EAAE;QAC1B,IAAI;YACF,MAAM,oHAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,OAAO,CAAC,EAAE;YAChD,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,OAAO,CAAE,EAAE;YACjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC7B,OAAO;mBAAI;aAAS,CAAC,IAAI;uDAAC,CAAC,GAAG;oBAC5B,MAAM,SAAS,CAAC,CAAC,QAAQ;oBACzB,MAAM,SAAS,CAAC,CAAC,QAAQ;oBAEzB,IAAI,UAAU,MAAM,OAAO;oBAC3B,IAAI,UAAU,MAAM,OAAO,CAAC;oBAE5B,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,CAAC,IAAI;oBAC3D,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,IAAI,CAAC;oBAC3D,OAAO;gBACT;;QACF;8CAAG;QAAC;QAAU;QAAS;KAAc;IAErC,MAAM,kBAAkB,CAAC,UAAqB,eAAe;YAAE,QAAQ;YAAM;QAAQ;IACrF,MAAM,mBAAmB,IAAM,eAAe;YAAE,QAAQ;YAAO,SAAS;QAAK;IAE7E,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA3Ea", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format date ke string (misal: Jan 1, 2024)\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });\n}\n\n// Format waktu (misal: 14:30)\nexport function formatTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });\n}\n\n// Format waktu relatif (misal: 2 hours ago)\nexport function formatRelativeTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diff = (now.getTime() - d.getTime()) / 1000;\n  if (diff < 60) return `${Math.floor(diff)} seconds ago`;\n  if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;\n  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;\n  return `${Math.floor(diff / 86400)} days ago`;\n}\n\n// Format angka (misal: 1,234)\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('en-US');\n}\n\n// Status color untuk badge\nexport function getStatusColor(status: string): string {\n  switch (status) {\n    case 'active': return 'bg-green-100 text-green-800';\n    case 'inactive': return 'bg-gray-100 text-gray-800';\n    case 'draft': return 'bg-yellow-100 text-yellow-800';\n    case 'completed': return 'bg-blue-100 text-blue-800';\n    default: return 'bg-gray-100 text-gray-800';\n  }\n}\n\n// Ekstrak pesan error dari objek error\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message;\n  if (typeof error === 'string') return error;\n  return 'Unknown error';\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QAAE,MAAM;QAAW,OAAO;QAAS,KAAK;IAAU;AACzF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QAAE,MAAM;QAAW,QAAQ;IAAU;AAC5E;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAC7C,IAAI,OAAO,IAAI,OAAO,AAAC,GAAmB,OAAjB,KAAK,KAAK,CAAC,OAAM;IAC1C,IAAI,OAAO,MAAM,OAAO,AAAC,GAAwB,OAAtB,KAAK,KAAK,CAAC,OAAO,KAAI;IACjD,IAAI,OAAO,OAAO,OAAO,AAAC,GAA0B,OAAxB,KAAK,KAAK,CAAC,OAAO,OAAM;IACpD,OAAO,AAAC,GAA2B,OAAzB,KAAK,KAAK,CAAC,OAAO,QAAO;AACrC;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAa,OAAO;QACzB;YAAS,OAAO;IAClB;AACF;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  children,\n  disabled,\n  ...props\n}) => {\n  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n  \n  const variants = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700',\n    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',\n    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50',\n    ghost: 'text-gray-600 hover:bg-gray-100',\n    destructive: 'bg-red-600 text-white hover:bg-red-700'\n  };\n\n  const sizes = {\n    sm: 'h-8 px-3 text-sm',\n    md: 'h-10 px-4 text-sm',\n    lg: 'h-12 px-6 text-base'\n  };\n\n  return (\n    <button\n      className={cn(\n        baseStyles,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button; "], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,SAAgC;QAAC,EACrC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;KA7CM;uCA+CS", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/admin/trainers/TrainersHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Plus } from 'lucide-react';\nimport Button from '@/components/ui/Button';\n\nconst TrainersHeader: React.FC = () => {\n  return (\n    <div className=\"flex justify-between items-center\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">Trainers</h1>\n        <p className=\"text-gray-600\">Manage AI trainers and their configurations</p>\n      </div>\n      <Link href=\"/admin/dashboard/trainers/create\">\n        <Button>\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Create Trainer\n        </Button>\n      </Link>\n    </div>\n  );\n};\n\nexport default TrainersHeader;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOA,MAAM,iBAA2B;IAC/B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAE/B,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,6LAAC,qIAAA,CAAA,UAAM;;sCACL,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM3C;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/admin/trainers/TrainersStats.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Settings, Users, MessageSquare } from 'lucide-react';\nimport { Trainer } from '@/types';\n\ninterface TrainersStatsProps {\n  trainers: Trainer[];\n}\n\nconst StatCard: React.FC<{ icon: React.ReactNode; label: string; value: string | number }> = ({ icon, label, value }) => (\n  <div className=\"bg-white p-6 rounded-lg shadow\">\n    <div className=\"flex items-center\">\n      {icon}\n      <div className=\"ml-4\">\n        <p className=\"text-sm font-medium text-gray-600\">{label}</p>\n        <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n      </div>\n    </div>\n  </div>\n);\n\nconst TrainersStats: React.FC<TrainersStatsProps> = ({ trainers }) => {\n  const activeTrainers = trainers.filter(t => t.status === 'active').length;\n  const totalMembers = trainers.reduce((sum, t) => sum + (t.memberCount || 0), 0);\n  const totalSubmodules = trainers.reduce((sum, t) => sum + (t.submoduleCount || 0), 0);\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <StatCard\n        icon={<Settings className=\"h-8 w-8 text-blue-600\" />}\n        label=\"Total Trainers\"\n        value={trainers.length}\n      />\n      <StatCard\n        icon={<Users className=\"h-8 w-8 text-green-600\" />}\n        label=\"Active Trainers\"\n        value={activeTrainers}\n      />\n      <StatCard\n        icon={<MessageSquare className=\"h-8 w-8 text-purple-600\" />}\n        label=\"Total Members\"\n        value={totalMembers}\n      />\n      <StatCard\n        icon={<Settings className=\"h-8 w-8 text-orange-600\" />}\n        label=\"Total Submodules\"\n        value={totalSubmodules}\n      />\n    </div>\n  );\n};\n\nexport default TrainersStats;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAHA;;;AAUA,MAAM,WAAuF;QAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;yBAClH,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;gBACZ;8BACD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;;KANnD;AAYN,MAAM,gBAA8C;QAAC,EAAE,QAAQ,EAAE;IAC/D,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IACzE,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,WAAW,IAAI,CAAC,GAAG;IAC7E,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,cAAc,IAAI,CAAC,GAAG;IAEnF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAM;gBACN,OAAO,SAAS,MAAM;;;;;;0BAExB,6LAAC;gBACC,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,OAAM;gBACN,OAAO;;;;;;0BAET,6LAAC;gBACC,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;gBAC/B,OAAM;gBACN,OAAO;;;;;;0BAET,6LAAC;gBACC,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,OAAM;gBACN,OAAO;;;;;;;;;;;;AAIf;MA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/ui/Table.tsx"], "sourcesContent": ["import React from 'react';\nimport { ChevronUp, ChevronDown } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface Column<T> {\n  key: keyof T;\n  label: string;\n  sortable?: boolean;\n  render?: (value: T[keyof T], row: T) => React.ReactNode;\n  className?: string;\n}\n\ninterface TableProps<T> {\n  data: T[];\n  columns: Column<T>[];\n  sortKey?: keyof T;\n  sortDirection?: 'asc' | 'desc';\n  onSort?: (key: keyof T) => void;\n  className?: string;\n  emptyMessage?: string;\n}\n\nfunction Table<T extends { id: React.Key }>({\n  data,\n  columns,\n  sortKey,\n  sortDirection,\n  onSort,\n  className,\n  emptyMessage = 'No data available'\n}: TableProps<T>) {\n  const handleSort = (key: keyof T) => {\n    if (onSort) {\n      onSort(key);\n    }\n  };\n\n  return (\n    <div className={cn('overflow-hidden rounded-lg border border-gray-200', className)}>\n      <table className=\"min-w-full divide-y divide-gray-200\">\n        <thead className=\"bg-gray-50\">\n          <tr>\n            {columns.map((column) => (\n              <th\n                key={String(column.key)}\n                className={cn(\n                  'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',\n                  column.sortable && 'cursor-pointer hover:bg-gray-100',\n                  column.className\n                )}\n                onClick={() => column.sortable && handleSort(column.key)}\n              >\n                <div className=\"flex items-center space-x-1\">\n                  <span>{column.label}</span>\n                  {column.sortable && (\n                    <div className=\"flex flex-col\">\n                      <ChevronUp \n                        className={cn(\n                          'h-3 w-3',\n                          sortKey === column.key && sortDirection === 'asc' \n                            ? 'text-blue-600' \n                            : 'text-gray-400'\n                        )}\n                      />\n                      <ChevronDown \n                        className={cn(\n                          'h-3 w-3 -mt-1',\n                          sortKey === column.key && sortDirection === 'desc' \n                            ? 'text-blue-600' \n                            : 'text-gray-400'\n                        )}\n                      />\n                    </div>\n                  )}\n                </div>\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody className=\"bg-white divide-y divide-gray-200\">\n          {data.length === 0 ? (\n            <tr>\n              <td \n                colSpan={columns.length} \n                className=\"px-6 py-12 text-center text-gray-500\"\n              >\n                {emptyMessage}\n              </td>\n            </tr>\n          ) : (\n            data.map((row, index) => (\n              <tr key={index} className=\"hover:bg-gray-50\">\n                {columns.map((column) => (\n                  <td\n                    key={String(column.key)}\n                    className={cn(\n                      'px-6 py-4 whitespace-nowrap text-sm text-gray-900',\n                      column.className\n                    )}\n                  >\n                    {column.render \n                      ? column.render(row[column.key], row)\n                      : String(row[column.key] || '-')\n                    }\n                  </td>\n                ))}\n              </tr>\n            ))\n          )}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n\nexport default Table; "], "names": [], "mappings": ";;;;AACA;AAAA;AACA;;;;AAoBA,SAAS,MAAmC,KAQ5B;QAR4B,EAC1C,IAAI,EACJ,OAAO,EACP,OAAO,EACP,aAAa,EACb,MAAM,EACN,SAAS,EACT,eAAe,mBAAmB,EACpB,GAR4B;IAS1C,MAAM,aAAa,CAAC;QAClB,IAAI,QAAQ;YACV,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;kBACtE,cAAA,6LAAC;YAAM,WAAU;;8BACf,6LAAC;oBAAM,WAAU;8BACf,cAAA,6LAAC;kCACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kFACA,OAAO,QAAQ,IAAI,oCACnB,OAAO,SAAS;gCAElB,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,GAAG;0CAEvD,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM,OAAO,KAAK;;;;;;wCAClB,OAAO,QAAQ,kBACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mNAAA,CAAA,YAAS;oDACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,YAAY,OAAO,GAAG,IAAI,kBAAkB,QACxC,kBACA;;;;;;8DAGR,6LAAC,uNAAA,CAAA,cAAW;oDACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iBACA,YAAY,OAAO,GAAG,IAAI,kBAAkB,SACxC,kBACA;;;;;;;;;;;;;;;;;;+BAzBT,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;8BAmC9B,6LAAC;oBAAM,WAAU;8BACd,KAAK,MAAM,KAAK,kBACf,6LAAC;kCACC,cAAA,6LAAC;4BACC,SAAS,QAAQ,MAAM;4BACvB,WAAU;sCAET;;;;;;;;;;+BAIL,KAAK,GAAG,CAAC,CAAC,KAAK,sBACb,6LAAC;4BAAe,WAAU;sCACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA,OAAO,SAAS;8CAGjB,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,EAAE,OAC/B,OAAO,GAAG,CAAC,OAAO,GAAG,CAAC,IAAI;mCARzB,OAAO,OAAO,GAAG;;;;;2BAHnB;;;;;;;;;;;;;;;;;;;;;AAsBvB;KA3FS;uCA6FM", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/admin/trainers/TrainersTable.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Edit, Trash2, Users, MessageSquare, Settings } from 'lucide-react';\nimport Table from '@/components/ui/Table';\nimport Button from '@/components/ui/Button';\nimport { Trainer, TableColumn } from '@/types';\nimport { formatDate, getStatusColor } from '@/lib/utils';\n\ninterface TrainersTableProps {\n  trainers: Trainer[];\n  onSort: (key: keyof Trainer) => void;\n  sortKey: keyof Trainer;\n  sortDirection: 'asc' | 'desc';\n  onDelete: (trainer: Trainer) => void;\n}\n\nconst TrainersTable: React.FC<TrainersTableProps> = ({\n  trainers,\n  onSort,\n  sortKey,\n  sortDirection,\n  onDelete,\n}) => {\n  const columns: TableColumn<Trainer>[] = [\n    {\n      key: 'name',\n      label: 'Name',\n      sortable: true,\n      render: (value, row) => (\n        <div className=\"flex items-center\">\n          <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3\">\n            <span className=\"text-sm font-medium text-blue-600\">\n              {(value as string).charAt(0).toUpperCase()}\n            </span>\n          </div>\n          <div>\n            <div className=\"font-medium text-gray-900\">{value as string}</div>\n            <div className=\"text-sm text-gray-500\">{row.description}</div>\n          </div>\n        </div>\n      )\n    },\n    {\n      key: 'status',\n      label: 'Status',\n      sortable: true,\n      render: (value) => (\n        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(value as string)}`}>\n          {value as string}\n        </span>\n      )\n    },\n    {\n      key: 'submoduleCount',\n      label: 'Submodules',\n      render: (value) => (\n        <div className=\"flex items-center\">\n          <Settings className=\"h-4 w-4 text-gray-400 mr-1\" />\n          {value as number || 0}\n        </div>\n      )\n    },\n    {\n      key: 'memberCount',\n      label: 'Members',\n      render: (value) => (\n        <div className=\"flex items-center\">\n          <Users className=\"h-4 w-4 text-gray-400 mr-1\" />\n          {value as number || 0}\n        </div>\n      )\n    },\n    {\n      key: 'conversationCount',\n      label: 'Conversations',\n      render: (value) => (\n        <div className=\"flex items-center\">\n          <MessageSquare className=\"h-4 w-4 text-gray-400 mr-1\" />\n          {value as number || 0}\n        </div>\n      )\n    },\n    {\n      key: 'created_at',\n      label: 'Created',\n      sortable: true,\n      render: (value) => formatDate(value as string)\n    },\n    {\n      key: 'id',\n      label: 'Actions',\n      render: (value, row) => (\n        <div className=\"flex space-x-2\">\n          <Link href={`/admin/dashboard/trainers/${row.id}`}>\n            <Button variant=\"ghost\" size=\"sm\">\n              <Edit className=\"h-4 w-4\" />\n            </Button>\n          </Link>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\"\n            onClick={() => onDelete(row)}\n          >\n            <Trash2 className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"bg-white rounded-lg shadow\">\n      <Table\n        data={trainers}\n        columns={columns}\n        sortKey={sortKey}\n        sortDirection={sortDirection}\n        onSort={onSort}\n        emptyMessage=\"No trainers found. Create your first trainer to get started.\"\n      />\n    </div>\n  );\n};\n\nexport default TrainersTable;"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AARA;;;;;;;AAkBA,MAAM,gBAA8C;QAAC,EACnD,QAAQ,EACR,MAAM,EACN,OAAO,EACP,aAAa,EACb,QAAQ,EACT;IACC,MAAM,UAAkC;QACtC;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAO,oBACd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CACb,AAAC,MAAiB,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sCAG5C,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAA6B;;;;;;8CAC5C,6LAAC;oCAAI,WAAU;8CAAyB,IAAI,WAAW;;;;;;;;;;;;;;;;;;QAI/D;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAW,AAAC,4DAA2F,OAAhC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;8BACzF;;;;;;QAGP;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,SAAmB;;;;;;;QAG1B;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAChB,SAAmB;;;;;;;QAG1B;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBACxB,SAAmB;;;;;;;QAG1B;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,QAAU,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;QAChC;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAO,oBACd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,AAAC,6BAAmC,OAAP,IAAI,EAAE;sCAC7C,cAAA,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,6LAAC,8MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGpB,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS;sCAExB,cAAA,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;QAI1B;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,UAAK;YACJ,MAAM;YACN,SAAS;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,cAAa;;;;;;;;;;;AAIrB;KA1GM;uCA4GS", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/ui/Modal.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  className?: string;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  className\n}) => {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl'\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex min-h-screen items-center justify-center p-4\">\n        {/* Overlay */}\n        <div \n          className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n          onClick={onClose}\n        />\n        \n        {/* Modal */}\n        <div className={cn(\n          'relative w-full transform rounded-lg bg-white shadow-xl transition-all',\n          sizeClasses[size],\n          className\n        )}>\n          {/* Header */}\n          {title && (\n            <div className=\"flex items-center justify-between border-b border-gray-200 px-6 py-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n              <button\n                onClick={onClose}\n                className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n              >\n                <X className=\"h-5 w-5\" />\n              </button>\n            </div>\n          )}\n          \n          {/* Content */}\n          <div className=\"px-6 py-4\">\n            {children}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAWA,MAAM,QAA8B;QAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,SAAS,EACV;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,0EACA,WAAW,CAAC,KAAK,EACjB;;wBAGC,uBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAvEM;KAAA;uCAyES", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/components/admin/trainers/DeleteTrainerModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Modal from '@/components/ui/Modal';\nimport Button from '@/components/ui/Button';\nimport { Trainer } from '@/types';\n\ninterface DeleteTrainerModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => void;\n  trainer: Trainer | null;\n}\n\nconst DeleteTrainerModal: React.FC<DeleteTrainerModalProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  trainer,\n}) => {\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Delete Trainer\"\n      size=\"sm\"\n    >\n      <div className=\"space-y-4\">\n        <p className=\"text-gray-600\">\n          Are you sure you want to delete trainer &quot;{trainer?.name}&quot;? \n          This action cannot be undone and will remove all associated submodules and conversations.\n        </p>\n        <div className=\"flex justify-end space-x-3\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"destructive\"\n            onClick={onConfirm}\n          >\n            Delete\n          </Button>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default DeleteTrainerModal;"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAcA,MAAM,qBAAwD;QAAC,EAC7D,MAAM,EACN,OAAO,EACP,SAAS,EACT,OAAO,EACR;IACC,qBACE,6LAAC,oIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;;wBAAgB;wBACoB,oBAAA,8BAAA,QAAS,IAAI;wBAAC;;;;;;;8BAG/D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS;sCACV;;;;;;sCAGD,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAnCM;uCAqCS", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/app/admin/dashboard/trainers/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useTrainers } from '@/hooks/useTrainers';\nimport TrainersHeader from '@/components/admin/trainers/TrainersHeader';\nimport TrainersStats from '@/components/admin/trainers/TrainersStats';\nimport TrainersTable from '@/components/admin/trainers/TrainersTable';\nimport DeleteTrainerModal from '@/components/admin/trainers/DeleteTrainerModal';\n\nconst TrainersPage: React.FC = () => {\n  const {\n    trainers,\n    loading,\n    deleteModal,\n    sortKey,\n    sortDirection,\n    handleSort,\n    handleDelete,\n    sortedTrainers,\n    openDeleteModal,\n    closeDeleteModal,\n  } = useTrainers();\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <TrainersHeader />\n      <TrainersStats trainers={trainers} />\n      <TrainersTable\n        trainers={sortedTrainers}\n        onSort={handleSort}\n        sortKey={sortKey}\n        sortDirection={sortDirection}\n        onDelete={openDeleteModal}\n      />\n      <DeleteTrainerModal\n        isOpen={deleteModal.isOpen}\n        onClose={closeDeleteModal}\n        onConfirm={handleDelete}\n        trainer={deleteModal.trainer}\n      />\n    </div>\n  );\n};\n\nexport default TrainersPage;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,eAAyB;;IAC7B,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,WAAW,EACX,OAAO,EACP,aAAa,EACb,UAAU,EACV,YAAY,EACZ,cAAc,EACd,eAAe,EACf,gBAAgB,EACjB,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEd,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,4JAAA,CAAA,UAAc;;;;;0BACf,6LAAC,2JAAA,CAAA,UAAa;gBAAC,UAAU;;;;;;0BACzB,6LAAC,2JAAA,CAAA,UAAa;gBACZ,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACT,eAAe;gBACf,UAAU;;;;;;0BAEZ,6LAAC,gKAAA,CAAA,UAAkB;gBACjB,QAAQ,YAAY,MAAM;gBAC1B,SAAS;gBACT,WAAW;gBACX,SAAS,YAAY,OAAO;;;;;;;;;;;;AAIpC;GAzCM;;QAYA,8HAAA,CAAA,cAAW;;;KAZX;uCA2CS", "debugId": null}}]}