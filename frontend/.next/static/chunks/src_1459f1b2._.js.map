{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/config/index.ts"], "sourcesContent": ["// Environment configuration\nconst config = {\n  // API Configuration\n  api: {\n    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002/api',\n    timeout: 10000, // 10 seconds\n  },\n  \n  // WebSocket Configuration\n  websocket: {\n    url: process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3002',\n    reconnectAttempts: 5,\n    reconnectDelay: 1000,\n  },\n  \n  // App Configuration\n  app: {\n    name: process.env.NEXT_PUBLIC_APP_NAME || 'AI Trainer Admin',\n    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',\n    environment: process.env.NEXT_PUBLIC_NODE_ENV || 'development',\n  },\n  \n  // Authentication\n  auth: {\n    tokenKey: 'auth_token',\n    tokenExpiry: 24 * 60 * 60 * 1000, // 24 hours in milliseconds\n  },\n  \n  // UI Configuration\n  ui: {\n    defaultPageSize: 10,\n    maxPageSize: 100,\n    autoRefreshInterval: 5000, // 5 seconds\n  },\n  \n  // Feature Flags\n  features: {\n    enableWebSocket: true,\n    enableNotifications: true,\n    enableAnalytics: true,\n  }\n};\n\nexport default config; "], "names": [], "mappings": "AAAA,4BAA4B;;;;AAIf;AAHb,MAAM,SAAS;IACb,oBAAoB;IACpB,KAAK;QACH,SAAS,iEAAmC;QAC5C,SAAS;IACX;IAEA,0BAA0B;IAC1B,WAAW;QACT,KAAK,6DAAkC;QACvC,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,oBAAoB;IACpB,KAAK;QACH,MAAM,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;QAC1C,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI;QAChD,aAAa,mDAAoC;IACnD;IAEA,iBAAiB;IACjB,MAAM;QACJ,UAAU;QACV,aAAa,KAAK,KAAK,KAAK;IAC9B;IAEA,mBAAmB;IACnB,IAAI;QACF,iBAAiB;QACjB,aAAa;QACb,qBAAqB;IACvB;IAEA,gBAAgB;IAChB,UAAU;QACR,iBAAiB;QACjB,qBAAqB;QACrB,iBAAiB;IACnB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport config from '@/config';\n\n// Create axios instance\nconst apiClient = axios.create({\n  baseURL: config.api.baseURL,\n  timeout: config.api.timeout,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor untuk menambahkan token\napiClient.interceptors.request.use(\n  (requestConfig) => {\n    const token = localStorage.getItem(config.auth.tokenKey);\n    if (token) {\n      requestConfig.headers.Authorization = `Bearer ${token}`;\n    }\n    return requestConfig;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor untuk handle errors\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem(config.auth.tokenKey);\n      window.location.href = '/auth/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// API functions\nexport const api = {\n  // Auth\n  auth: {\n    login: (credentials: { email: string; password: string; userType: string }) =>\n      apiClient.post('/auth/login', credentials),\n    logout: () => apiClient.post('/auth/logout'),\n    getProfile: () => apiClient.get('/auth/profile'),\n    registerAdmin: (data: { name: string; email: string; password: string; role: string }) =>\n      apiClient.post('/auth/register/admin', data),\n    registerMember: (data: { name: string; email: string; password: string }) =>\n      apiClient.post('/auth/register/member', data),\n  },\n\n  // Admins\n  admins: {\n    getAll: (params?: { page?: number; limit?: number; search?: string }) =>\n      apiClient.get('/admins', { params }),\n    getById: (id: number) => apiClient.get(`/admins/${id}`),\n    create: (data: { name: string; email: string; password: string; role: string }) =>\n      apiClient.post('/auth/register/admin', data),\n    update: (id: number, data: Partial<{ name: string; email: string; role: string; isActive: boolean }>) =>\n      apiClient.put(`/admins/${id}`, data),\n    delete: (id: number) => apiClient.delete(`/admins/${id}`),\n  },\n\n  // Trainers\n  trainers: {\n    getAll: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>\n      apiClient.get('/trainers', { params }),\n    getById: (id: number) => apiClient.get(`/trainers/${id}`),\n    create: (data: { name: string; systemPrompt: string; description?: string; status?: string }) =>\n      apiClient.post('/trainers', data),\n    update: (id: number, data: Partial<{ name: string; systemPrompt: string; description: string; status: string }>) =>\n      apiClient.put(`/trainers/${id}`, data),\n    delete: (id: number) => apiClient.delete(`/trainers/${id}`),\n    assign: (id: number, memberIds: number[]) =>\n      apiClient.post(`/trainers/${id}/assign`, { memberIds }),\n    unassign: (id: number, memberId: number) =>\n      apiClient.delete(`/trainers/${id}/assign/${memberId}`),\n    getStatistics: (id: number) => apiClient.get(`/trainers/${id}/statistics`),\n  },\n\n  // Trainer Submodules\n  submodules: {\n    getByTrainer: (trainerId: number) =>\n      apiClient.get(`/trainers/${trainerId}/submodules`),\n    create: (trainerId: number, data: { name: string; systemPrompt: string; description?: string; status?: string }) =>\n      apiClient.post(`/trainers/${trainerId}/submodules`, data),\n    getById: (id: number) => apiClient.get(`/submodules/${id}`),\n    update: (id: number, data: Partial<{ name: string; systemPrompt: string; description: string; status: string }>) =>\n      apiClient.put(`/submodules/${id}`, data),\n    delete: (id: number) => apiClient.delete(`/submodules/${id}`),\n    reorder: (trainerId: number, submoduleIds: number[]) =>\n      apiClient.put(`/trainers/${trainerId}/submodules/reorder`, { submoduleIds }),\n    validatePrompt: (systemPrompt: string) =>\n      apiClient.post('/submodules/validate-prompt', { systemPrompt }),\n  },\n\n  // Members\n  members: {\n    getAll: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>\n      apiClient.get('/members', { params }),\n    getById: (id: number) => apiClient.get(`/members/${id}`),\n    create: (data: { name: string; email: string; password: string }) =>\n      apiClient.post('/members', data),\n    update: (id: number, data: Partial<{ name: string; email: string; isActive: boolean }>) =>\n      apiClient.put(`/members/${id}`, data),\n    delete: (id: number) => apiClient.delete(`/members/${id}`),\n    getAssignedTrainers: (memberId: number) =>\n      apiClient.get(`/members/${memberId}/trainers`),\n  },\n\n  // Monitoring\n  monitoring: {\n    getDashboard: () => apiClient.get('/monitoring/dashboard'),\n    getSuperAdminDashboard: () => apiClient.get('/monitoring/super-admin/dashboard'),\n    getConversations: (params?: { trainerId?: number; memberId?: number; dateFrom?: string; dateTo?: string }) =>\n      apiClient.get('/monitoring/conversations', { params }),\n    getStatistics: () => apiClient.get('/monitoring/statistics'),\n  },\n\n  // Progress\n  progress: {\n    getByMember: (memberId: number) => apiClient.get(`/progress/member/${memberId}`),\n    getByTrainer: (trainerId: number) => apiClient.get(`/progress/trainer/${trainerId}`),\n    getTrends: (memberId: number, timeRange: string) =>\n      apiClient.get(`/progress/member/${memberId}/trends`, { params: { timeRange } }),\n    generateReport: (filters: {\n      trainerId?: number;\n      memberId?: number;\n      dateFrom?: string;\n      dateTo?: string;\n    }) => apiClient.post('/progress/report', filters),\n  },\n\n  // Chat\n  chat: {\n    initialize: (trainerId: number) =>\n      apiClient.post(`/chat/${trainerId}/initialize`),\n    sendMessage: (trainerId: number, data: { message: string; sessionId: string }) =>\n      apiClient.post(`/chat/${trainerId}/message`, data),\n    getHistory: (trainerId: number, params?: { page?: number; limit?: number }) =>\n      apiClient.get(`/chat/${trainerId}/history`, { params }),\n  },\n\n  // System\n  system: {\n    getHealth: () => apiClient.get('/system/health'),\n    getCacheStatus: () => apiClient.get('/system/cache/status'),\n    flushCache: () => apiClient.post('/system/cache/flush'),\n  },\n\n  // Convenience methods for easier usage\n  getTrainers: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>\n    apiClient.get('/trainers', { params }),\n  deleteTrainer: (id: number) => apiClient.delete(`/trainers/${id}`),\n};\n\nexport default apiClient;"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,wBAAwB;AACxB,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,yHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,OAAO;IAC3B,SAAS,yHAAA,CAAA,UAAM,CAAC,GAAG,CAAC,OAAO;IAC3B,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,8CAA8C;AAC9C,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ;IACvD,IAAI,OAAO;QACT,cAAc,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2CAA2C;AAC3C,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;QACK;IAAJ,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;QAClC,2BAA2B;QAC3B,aAAa,UAAU,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ;QAC5C,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,MAAM;IACjB,OAAO;IACP,MAAM;QACJ,OAAO,CAAC,cACN,UAAU,IAAI,CAAC,eAAe;QAChC,QAAQ,IAAM,UAAU,IAAI,CAAC;QAC7B,YAAY,IAAM,UAAU,GAAG,CAAC;QAChC,eAAe,CAAC,OACd,UAAU,IAAI,CAAC,wBAAwB;QACzC,gBAAgB,CAAC,OACf,UAAU,IAAI,CAAC,yBAAyB;IAC5C;IAEA,SAAS;IACT,QAAQ;QACN,QAAQ,CAAC,SACP,UAAU,GAAG,CAAC,WAAW;gBAAE;YAAO;QACpC,SAAS,CAAC,KAAe,UAAU,GAAG,CAAC,AAAC,WAAa,OAAH;QAClD,QAAQ,CAAC,OACP,UAAU,IAAI,CAAC,wBAAwB;QACzC,QAAQ,CAAC,IAAY,OACnB,UAAU,GAAG,CAAC,AAAC,WAAa,OAAH,KAAM;QACjC,QAAQ,CAAC,KAAe,UAAU,MAAM,CAAC,AAAC,WAAa,OAAH;IACtD;IAEA,WAAW;IACX,UAAU;QACR,QAAQ,CAAC,SACP,UAAU,GAAG,CAAC,aAAa;gBAAE;YAAO;QACtC,SAAS,CAAC,KAAe,UAAU,GAAG,CAAC,AAAC,aAAe,OAAH;QACpD,QAAQ,CAAC,OACP,UAAU,IAAI,CAAC,aAAa;QAC9B,QAAQ,CAAC,IAAY,OACnB,UAAU,GAAG,CAAC,AAAC,aAAe,OAAH,KAAM;QACnC,QAAQ,CAAC,KAAe,UAAU,MAAM,CAAC,AAAC,aAAe,OAAH;QACtD,QAAQ,CAAC,IAAY,YACnB,UAAU,IAAI,CAAC,AAAC,aAAe,OAAH,IAAG,YAAU;gBAAE;YAAU;QACvD,UAAU,CAAC,IAAY,WACrB,UAAU,MAAM,CAAC,AAAC,aAAyB,OAAb,IAAG,YAAmB,OAAT;QAC7C,eAAe,CAAC,KAAe,UAAU,GAAG,CAAC,AAAC,aAAe,OAAH,IAAG;IAC/D;IAEA,qBAAqB;IACrB,YAAY;QACV,cAAc,CAAC,YACb,UAAU,GAAG,CAAC,AAAC,aAAsB,OAAV,WAAU;QACvC,QAAQ,CAAC,WAAmB,OAC1B,UAAU,IAAI,CAAC,AAAC,aAAsB,OAAV,WAAU,gBAAc;QACtD,SAAS,CAAC,KAAe,UAAU,GAAG,CAAC,AAAC,eAAiB,OAAH;QACtD,QAAQ,CAAC,IAAY,OACnB,UAAU,GAAG,CAAC,AAAC,eAAiB,OAAH,KAAM;QACrC,QAAQ,CAAC,KAAe,UAAU,MAAM,CAAC,AAAC,eAAiB,OAAH;QACxD,SAAS,CAAC,WAAmB,eAC3B,UAAU,GAAG,CAAC,AAAC,aAAsB,OAAV,WAAU,wBAAsB;gBAAE;YAAa;QAC5E,gBAAgB,CAAC,eACf,UAAU,IAAI,CAAC,+BAA+B;gBAAE;YAAa;IACjE;IAEA,UAAU;IACV,SAAS;QACP,QAAQ,CAAC,SACP,UAAU,GAAG,CAAC,YAAY;gBAAE;YAAO;QACrC,SAAS,CAAC,KAAe,UAAU,GAAG,CAAC,AAAC,YAAc,OAAH;QACnD,QAAQ,CAAC,OACP,UAAU,IAAI,CAAC,YAAY;QAC7B,QAAQ,CAAC,IAAY,OACnB,UAAU,GAAG,CAAC,AAAC,YAAc,OAAH,KAAM;QAClC,QAAQ,CAAC,KAAe,UAAU,MAAM,CAAC,AAAC,YAAc,OAAH;QACrD,qBAAqB,CAAC,WACpB,UAAU,GAAG,CAAC,AAAC,YAAoB,OAAT,UAAS;IACvC;IAEA,aAAa;IACb,YAAY;QACV,cAAc,IAAM,UAAU,GAAG,CAAC;QAClC,wBAAwB,IAAM,UAAU,GAAG,CAAC;QAC5C,kBAAkB,CAAC,SACjB,UAAU,GAAG,CAAC,6BAA6B;gBAAE;YAAO;QACtD,eAAe,IAAM,UAAU,GAAG,CAAC;IACrC;IAEA,WAAW;IACX,UAAU;QACR,aAAa,CAAC,WAAqB,UAAU,GAAG,CAAC,AAAC,oBAA4B,OAAT;QACrE,cAAc,CAAC,YAAsB,UAAU,GAAG,CAAC,AAAC,qBAA8B,OAAV;QACxE,WAAW,CAAC,UAAkB,YAC5B,UAAU,GAAG,CAAC,AAAC,oBAA4B,OAAT,UAAS,YAAU;gBAAE,QAAQ;oBAAE;gBAAU;YAAE;QAC/E,gBAAgB,CAAC,UAKX,UAAU,IAAI,CAAC,oBAAoB;IAC3C;IAEA,OAAO;IACP,MAAM;QACJ,YAAY,CAAC,YACX,UAAU,IAAI,CAAC,AAAC,SAAkB,OAAV,WAAU;QACpC,aAAa,CAAC,WAAmB,OAC/B,UAAU,IAAI,CAAC,AAAC,SAAkB,OAAV,WAAU,aAAW;QAC/C,YAAY,CAAC,WAAmB,SAC9B,UAAU,GAAG,CAAC,AAAC,SAAkB,OAAV,WAAU,aAAW;gBAAE;YAAO;IACzD;IAEA,SAAS;IACT,QAAQ;QACN,WAAW,IAAM,UAAU,GAAG,CAAC;QAC/B,gBAAgB,IAAM,UAAU,GAAG,CAAC;QACpC,YAAY,IAAM,UAAU,IAAI,CAAC;IACnC;IAEA,uCAAuC;IACvC,aAAa,CAAC,SACZ,UAAU,GAAG,CAAC,aAAa;YAAE;QAAO;IACtC,eAAe,CAAC,KAAe,UAAU,MAAM,CAAC,AAAC,aAAe,OAAH;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/store/auth.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { api } from '@/lib/api';\nimport { User, LoginCredentials, AuthState } from '@/types';\nimport config from '@/config';\n\ninterface AuthStore extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => void;\n  loadUser: () => Promise<void>;\n  setUser: (user: User | null) => void;\n  setToken: (token: string | null) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useAuthStore = create<AuthStore>()(\n  persist(\n    (set) => ({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n\n      login: async (credentials: LoginCredentials) => {\n        set({ isLoading: true });\n        try {\n          const response = await api.auth.login(credentials);\n          const { user, token } = response.data.data;\n          \n          // Store token in localStorage\n          localStorage.setItem(config.auth.tokenKey, token);\n          \n          set({\n            user,\n            token,\n            isAuthenticated: true,\n            isLoading: false,\n          });\n        } catch (error) {\n          set({ isLoading: false });\n          throw error;\n        }\n      },\n\n      logout: () => {\n        localStorage.removeItem(config.auth.tokenKey);\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n        });\n      },\n\n      loadUser: async () => {\n        const token = localStorage.getItem(config.auth.tokenKey);\n        if (!token) return;\n\n        set({ isLoading: true });\n        try {\n          const response = await api.auth.getProfile();\n          const user = response.data.data.user;\n          \n          set({\n            user,\n            token,\n            isAuthenticated: true,\n            isLoading: false,\n          });\n        } catch {\n          // Token is invalid, clear it\n          localStorage.removeItem(config.auth.tokenKey);\n          set({\n            user: null,\n            token: null,\n            isAuthenticated: false,\n            isLoading: false,\n          });\n        }\n      },\n\n      setUser: (user: User | null) => {\n        set({ user, isAuthenticated: !!user });\n      },\n\n      setToken: (token: string | null) => {\n        set({ token });\n        if (token) {\n          localStorage.setItem(config.auth.tokenKey, token);\n        } else {\n          localStorage.removeItem(config.auth.tokenKey);\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n    }),\n    {\n      name: 'auth-store',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n); "], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;;;;;AAWO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,OAAO;QACP,iBAAiB;QACjB,WAAW;QAEX,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,KAAK,CAAC;gBACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;gBAE1C,8BAA8B;gBAC9B,aAAa,OAAO,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAE3C,IAAI;oBACF;oBACA;oBACA,iBAAiB;oBACjB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM;YACR;QACF;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ;YAC5C,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF;QAEA,UAAU;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ;YACvD,IAAI,CAAC,OAAO;YAEZ,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,UAAU;gBAC1C,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;gBAEpC,IAAI;oBACF;oBACA;oBACA,iBAAiB;oBACjB,WAAW;gBACb;YACF,EAAE,UAAM;gBACN,6BAA6B;gBAC7B,aAAa,UAAU,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ;gBAC5C,IAAI;oBACF,MAAM;oBACN,OAAO;oBACP,iBAAiB;oBACjB,WAAW;gBACb;YACF;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;QACtC;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;YACZ,IAAI,OAAO;gBACT,aAAa,OAAO,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC7C,OAAO;gBACL,aAAa,UAAU,CAAC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,QAAQ;YAC9C;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/meet_trainer/frontend/src/app/admin/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/auth';\nimport { \n  Home, \n  Users, \n  BookOpen, \n  MessageSquare, \n  BarChart3, \n  Settings, \n  LogOut,\n  Menu,\n  X,\n  UserCheck\n} from 'lucide-react';\nimport { useState } from 'react';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const { user, isAuthenticated, logout, loadUser } = useAuthStore();\n  const router = useRouter();\n\n  // Dynamic navigation based on user role\n  const navigation = [\n    { name: 'Dashboard', href: '/admin/dashboard', icon: Home },\n    { name: 'Trainers', href: '/admin/dashboard/trainers', icon: BookOpen },\n    { name: 'Members', href: '/admin/dashboard/members', icon: Users },\n    ...(user?.role === 'super_admin' ? [\n      { name: 'Admin Management', href: '/admin/dashboard/admin-management', icon: UserCheck },\n    ] : []),\n    { name: 'Monitoring', href: '/admin/dashboard/monitoring', icon: MessageSquare },\n    { name: 'Analytics', href: '/admin/dashboard/analytics', icon: BarChart3 },\n    { name: 'Settings', href: '/admin/dashboard/settings', icon: Settings },\n  ];\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      setIsLoading(true);\n      if (!isAuthenticated) {\n        await loadUser();\n        // Check again after loadUser completes\n        const { isAuthenticated: authStatus } = useAuthStore.getState();\n        if (!authStatus) {\n          router.push('/auth/login');\n          return;\n        }\n      }\n      setIsLoading(false);\n    };\n    \n    checkAuth();\n  }, [isAuthenticated, loadUser, router]);\n\n  const handleLogout = () => {\n    logout();\n    router.push('/auth/login');\n  };\n\n  if (isLoading || !isAuthenticated || !user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"fixed inset-y-0 left-0 flex flex-col w-64 bg-white\">\n          <div className=\"flex items-center justify-between h-16 px-4 border-b\">\n            <h1 className=\"text-xl font-semibold text-gray-900\">AI Trainer Admin</h1>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n            >\n              <X className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-2\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t\">\n            <button\n              onClick={handleLogout}\n              className=\"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 rounded-md hover:bg-red-50\"\n            >\n              <LogOut className=\"h-5 w-5 mr-3\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-1 min-h-0 bg-white border-r\">\n          <div className=\"flex items-center h-16 px-4 border-b\">\n            <h1 className=\"text-xl font-semibold text-gray-900\">AI Trainer Admin</h1>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-2\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <div className=\"h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-medium\">\n                  {user.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n                <p className=\"text-xs text-gray-500\">{user.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={handleLogout}\n              className=\"flex items-center w-full px-3 py-2 text-sm font-medium text-red-700 rounded-md hover:bg-red-50\"\n            >\n              <LogOut className=\"h-5 w-5 mr-3\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Mobile header */}\n        <div className=\"lg:hidden\">\n          <div className=\"flex items-center justify-between h-16 px-4 bg-white border-b\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n            <h1 className=\"text-lg font-semibold text-gray-900\">AI Trainer Admin</h1>\n            <div className=\"h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-white text-sm font-medium\">\n                {user.name.charAt(0).toUpperCase()}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;;AAmBe,SAAS,gBAAgB,KAIvC;QAJuC,EACtC,QAAQ,EAGT,GAJuC;;IAKtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAC/D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,wCAAwC;IACxC,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM,sMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,MAAM;YAAY,MAAM;YAA6B,MAAM,iNAAA,CAAA,WAAQ;QAAC;QACtE;YAAE,MAAM;YAAW,MAAM;YAA4B,MAAM,uMAAA,CAAA,QAAK;QAAC;WAC7D,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,gBAAgB;YACjC;gBAAE,MAAM;gBAAoB,MAAM;gBAAqC,MAAM,mNAAA,CAAA,YAAS;YAAC;SACxF,GAAG,EAAE;QACN;YAAE,MAAM;YAAc,MAAM;YAA+B,MAAM,2NAAA,CAAA,gBAAa;QAAC;QAC/E;YAAE,MAAM;YAAa,MAAM;YAA8B,MAAM,qNAAA,CAAA,YAAS;QAAC;QACzE;YAAE,MAAM;YAAY,MAAM;YAA6B,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACvE;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;uDAAY;oBAChB,aAAa;oBACb,IAAI,CAAC,iBAAiB;wBACpB,MAAM;wBACN,uCAAuC;wBACvC,MAAM,EAAE,iBAAiB,UAAU,EAAE,GAAG,uHAAA,CAAA,eAAY,CAAC,QAAQ;wBAC7D,IAAI,CAAC,YAAY;4BACf,OAAO,IAAI,CAAC;4BACZ;wBACF;oBACF;oBACA,aAAa;gBACf;;YAEA;QACF;oCAAG;QAAC;QAAiB;QAAU;KAAO;IAEtC,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,aAAa,CAAC,mBAAmB,CAAC,MAAM;QAC1C,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,AAAC,gCAAgE,OAAjC,cAAc,UAAU;;kCACtE,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;0CASpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;;;;;;sCASpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sDAGpC,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAqC,KAAK,IAAI;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;8CAGnD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;kCAOxC,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnKwB;;QAO8B,uHAAA,CAAA,eAAY;QACjD,qIAAA,CAAA,YAAS;;;KARF", "debugId": null}}]}